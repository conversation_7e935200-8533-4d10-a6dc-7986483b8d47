{"name": "frontend", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@aws-amplify/auth": "^6.13.0", "@aws-amplify/core": "^6.12.0", "@aws-sdk/client-cognito-identity-provider": "^3.817.0", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-masked-view/masked-view": "^0.3.2", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/native": "^7.1.6", "@react-navigation/native-stack": "^7.3.10", "amazon-cognito-identity-js": "^6.3.15", "aws-amplify": "^6.15.0", "expo-font": "^13.0.4", "expo-linear-gradient": "^14.0.2", "nativewind": "^4.1.23", "phosphor-react-native": "^2.3.1", "react": "19.0.0", "react-native": "0.78.1", "react-native-gesture-handler": "^2.25.0", "react-native-linear-gradient": "^2.8.3", "react-native-reanimated": "^3.17.3", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-native-splash-screen": "^3.3.0", "react-native-svg": "^15.11.2", "react-native-vector-icons": "^10.2.0", "tailwind-react-native-classnames": "^1.5.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.78.1", "@react-native/eslint-config": "0.78.1", "@react-native/metro-config": "0.78.1", "@react-native/typescript-config": "0.78.1", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "metro-react-native-babel-preset": "^0.77.0", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}