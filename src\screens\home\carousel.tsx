import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Dimensions,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Animated,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';

const {width} = Dimensions.get('window');

const CARD_WIDTH = 361; // Fixed width as per design
const CARD_HEIGHT = 250; // Fixed height as per design
const SPACING = 12;
const SIDE_SPACING = (width - CARD_WIDTH) / 2;

const data = [
  {
    id: '1',
    title: 'Repayment Due Today',
    text: 'Gentle Reminder: You have to repay <PERSON><PERSON>ham Rs. 15,000 today.',
    amount: '₹15,000',
    person: 'Saksham',
    urgency: 'high',
    dueDate: 'Today',
    gradientColors: ['#FF6B6B', '#FF8E53'],
    icon: '⚠️',
  },
  {
    id: '2',
    title: 'Upcoming Repayment',
    text: 'Heads-up: Repayment to Rohit of Rs. 10,000 is due tomorrow.',
    amount: '₹10,000',
    person: 'Rohit',
    urgency: 'medium',
    dueDate: 'Tomorrow',
    gradientColors: ['#4FACFE', '#00F2FE'],
    icon: '📅',
  },
  {
    id: '3',
    title: 'Payment Due Soon',
    text: 'Reminder: You owe Priya Rs. 5,000 in 3 days.',
    amount: '₹5,000',
    person: 'Priya',
    urgency: 'low',
    dueDate: 'In 3 days',
    gradientColors: ['#43E97B', '#38F9D7'],
    icon: '💰',
  },
];

// Add duplicates at start and end for smooth infinite scroll
const loopedData = [...data, ...data, ...data];

const RepaymentCarousel = () => {
  const navigation = useNavigation();
  const flatListRef = useRef<FlatList>(null);
  const [currentIndex, setCurrentIndex] = useState(data.length);
  const scrollX = useRef(new Animated.Value(0)).current;
  const [isScrolling, setIsScrolling] = useState(false);

  // Function to handle automatic scrolling
  const scrollToIndex = (index: number, animated = true) => {
    flatListRef.current?.scrollToIndex({
      index,
      animated,
    });
  };

  // Reset to middle set when reaching the ends
  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const contentOffset = event.nativeEvent.contentOffset.x;
    const currentIndex = Math.round(contentOffset / (CARD_WIDTH + SPACING));

    // If we're at the start of the last set
    if (currentIndex >= data.length * 2) {
      // Jump to middle set without animation
      scrollToIndex(currentIndex - data.length, false);
    }
    // If we're at the end of the first set
    else if (currentIndex <= data.length - 1) {
      // Jump to middle set without animation
      scrollToIndex(currentIndex + data.length, false);
    }
  };

  // Auto scroll effect
  useEffect(() => {
    let interval: NodeJS.Timeout;

    const startAutoScroll = () => {
      interval = setInterval(() => {
        if (!isScrolling) {
          const nextIndex = (currentIndex % data.length) + 1 + data.length;
          scrollToIndex(nextIndex);
        }
      }, 3000);
    };

    startAutoScroll();

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [currentIndex, isScrolling]);

  const onMomentumScrollEnd = (
    event: NativeSyntheticEvent<NativeScrollEvent>,
  ) => {
    const contentOffset = event.nativeEvent.contentOffset.x;
    const newIndex = Math.round(contentOffset / (CARD_WIDTH + SPACING));
    setCurrentIndex(newIndex % data.length);
    setIsScrolling(false);
  };

  const handleLater = () => {
    // Handle later action
  };

  const handleViewMore = () => {
    navigation.navigate('RepayMoney' as never);
  };

  return (
    <View style={styles.container}>
      <Animated.FlatList
        ref={flatListRef}
        data={loopedData}
        keyExtractor={(item, index) => `${item.id}-${index}`}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{
          paddingHorizontal: SIDE_SPACING,
        }}
        snapToInterval={CARD_WIDTH + SPACING}
        decelerationRate="fast"
        onScroll={Animated.event(
          [{nativeEvent: {contentOffset: {x: scrollX}}}],
          {
            useNativeDriver: true,
            listener: handleScroll,
          },
        )}
        scrollEventThrottle={16}
        onMomentumScrollEnd={onMomentumScrollEnd}
        onScrollBeginDrag={() => setIsScrolling(true)}
        initialScrollIndex={data.length}
        getItemLayout={(_, index) => ({
          length: CARD_WIDTH + SPACING,
          offset: (CARD_WIDTH + SPACING) * index,
          index,
        })}
        renderItem={({item, index}) => {
          const inputRange = [
            (index - 2) * (CARD_WIDTH + SPACING),
            (index - 1) * (CARD_WIDTH + SPACING),
            index * (CARD_WIDTH + SPACING),
          ];

          const scale = scrollX.interpolate({
            inputRange,
            outputRange: [0.95, 1, 0.95],
            extrapolate: 'clamp',
          });

          const opacity = scrollX.interpolate({
            inputRange,
            outputRange: [0.6, 1, 0.6],
            extrapolate: 'clamp',
          });

          return (
            <Animated.View
              style={[
                styles.cardContainer,
                {
                  width: CARD_WIDTH,
                  height: CARD_HEIGHT,
                  marginHorizontal: SPACING / 2,
                  transform: [{scale}],
                  opacity,
                },
              ]}>
              <LinearGradient
                colors={item.gradientColors}
                style={styles.card}
                start={{x: 0, y: 0}}
                end={{x: 1, y: 1}}>
                {/* Header with icon and urgency indicator */}
                <View style={styles.cardHeader}>
                  <View style={styles.iconContainer}>
                    <Text style={styles.icon}>{item.icon}</Text>
                  </View>
                  <View
                    style={[
                      styles.urgencyBadge,
                      item.urgency === 'high' && styles.urgencyHigh,
                      item.urgency === 'medium' && styles.urgencyMedium,
                      item.urgency === 'low' && styles.urgencyLow,
                    ]}>
                    <Text style={styles.urgencyText}>{item.dueDate}</Text>
                  </View>
                </View>

                {/* Main content */}
                <View style={styles.cardContent}>
                  <Text style={styles.title}>{item.title}</Text>
                  <Text style={styles.personAmount}>
                    <Text style={styles.person}>{item.person}</Text>
                    <Text style={styles.amount}> • {item.amount}</Text>
                  </Text>
                  <Text style={styles.description}>{item.text}</Text>
                </View>

                {/* Action buttons */}
                <View style={styles.buttonContainer}>
                  <TouchableOpacity
                    style={styles.laterButton}
                    onPress={handleLater}>
                    <Text style={styles.laterButtonText}>Later</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.payNowButton}
                    onPress={handleViewMore}>
                    <Text style={styles.payNowButtonText}>Pay Now</Text>
                  </TouchableOpacity>
                </View>

                {/* Decorative elements */}
                <View style={styles.decorativeCircle1} />
                <View style={styles.decorativeCircle2} />
              </LinearGradient>
            </Animated.View>
          );
        }}
      />

      <View style={styles.indicatorContainer}>
        {data.map((_, index) => (
          <View
            key={index}
            style={[
              styles.dot,
              currentIndex % data.length === index && styles.activeDot,
            ]}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: CARD_HEIGHT + 40, // Extra space for indicators
  },
  cardContainer: {
    borderRadius: 20,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    overflow: 'hidden',
  },
  card: {
    flex: 1,
    borderRadius: 20,
    padding: 20,
    position: 'relative',
    overflow: 'hidden',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  icon: {
    fontSize: 20,
  },
  urgencyBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
  },
  urgencyHigh: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
  },
  urgencyMedium: {
    backgroundColor: 'rgba(255, 255, 255, 0.85)',
  },
  urgencyLow: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  urgencyText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#333',
  },
  cardContent: {
    flex: 1,
    marginBottom: 20,
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
    color: '#FFFFFF',
    marginBottom: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: {width: 0, height: 1},
    textShadowRadius: 2,
  },
  personAmount: {
    marginBottom: 12,
  },
  person: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  amount: {
    fontSize: 18,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  description: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    lineHeight: 20,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 12,
  },
  laterButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    alignItems: 'center',
  },
  laterButtonText: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  payNowButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 25,
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  payNowButtonText: {
    fontSize: 14,
    color: '#333',
    fontWeight: '700',
  },
  decorativeCircle1: {
    position: 'absolute',
    top: -30,
    right: -30,
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  decorativeCircle2: {
    position: 'absolute',
    bottom: -20,
    left: -20,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
  },
  indicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(0, 102, 255, 0.3)',
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: '#0066FF',
    width: 20,
    borderRadius: 4,
    transform: [{scaleY: 1.2}],
  },
});

export default RepaymentCarousel;
